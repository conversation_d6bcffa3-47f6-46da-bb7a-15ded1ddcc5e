{"version": 2, "dgSpecHash": "7N3LC97ng3Q=", "success": true, "projectFilePath": "/home/<USER>/Code/ahmet-cetinkaya/nugone/NuGone.UnitTests/NuGone.UnitTests.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/coverlet.collector/6.0.2/coverlet.collector.6.0.2.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codecoverage/17.12.0/microsoft.codecoverage.17.12.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.net.test.sdk/17.12.0/microsoft.net.test.sdk.17.12.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.12.0/microsoft.testplatform.objectmodel.17.12.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.12.0/microsoft.testplatform.testhost.17.12.0.nupkg.sha512", "/home/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512", "/home/<USER>/.nuget/packages/xunit/2.9.2/xunit.2.9.2.nupkg.sha512", "/home/<USER>/.nuget/packages/xunit.abstractions/2.0.3/xunit.abstractions.2.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/xunit.analyzers/1.16.0/xunit.analyzers.1.16.0.nupkg.sha512", "/home/<USER>/.nuget/packages/xunit.assert/2.9.2/xunit.assert.2.9.2.nupkg.sha512", "/home/<USER>/.nuget/packages/xunit.core/2.9.2/xunit.core.2.9.2.nupkg.sha512", "/home/<USER>/.nuget/packages/xunit.extensibility.core/2.9.2/xunit.extensibility.core.2.9.2.nupkg.sha512", "/home/<USER>/.nuget/packages/xunit.extensibility.execution/2.9.2/xunit.extensibility.execution.2.9.2.nupkg.sha512", "/home/<USER>/.nuget/packages/xunit.runner.visualstudio/2.8.2/xunit.runner.visualstudio.2.8.2.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/9.0.5/microsoft.aspnetcore.app.ref.9.0.5.nupkg.sha512"], "logs": []}