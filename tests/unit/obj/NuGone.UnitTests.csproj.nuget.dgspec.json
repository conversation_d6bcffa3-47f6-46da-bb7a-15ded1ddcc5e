{"format": 1, "restore": {"/home/<USER>/Code/ahmet-cetinkaya/nugone/NuGone.UnitTests/NuGone.UnitTests.csproj": {}}, "projects": {"/home/<USER>/Code/ahmet-cetinkaya/nugone/NuGone.UnitTests/NuGone.UnitTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Code/ahmet-cetinkaya/nugone/NuGone.UnitTests/NuGone.UnitTests.csproj", "projectName": "NuGone.UnitTests", "projectPath": "/home/<USER>/Code/ahmet-cetinkaya/nugone/NuGone.UnitTests/NuGone.UnitTests.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Code/ahmet-cetinkaya/nugone/NuGone.UnitTests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.106/PortableRuntimeIdentifierGraph.json"}}}}}