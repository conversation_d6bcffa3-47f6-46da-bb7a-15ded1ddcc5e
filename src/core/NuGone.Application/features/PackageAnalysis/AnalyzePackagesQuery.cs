using MediatR;
using NuGone.Application.Shared.DTOs;

namespace NuGone.Application.Features.PackageAnalysis;

/// <summary>
/// Query to analyze packages in a project or solution
/// </summary>
public class AnalyzePackagesQuery : IRequest<PackageAnalysisResult>
{
    public string ProjectOrSolutionPath { get; }
    public bool DryRun { get; }
    public IReadOnlyList<string> ExcludePackages { get; }
    public IReadOnlyList<string> ExcludeNamespaces { get; }
    public IReadOnlyList<string> ExcludeFiles { get; }

    public AnalyzePackagesQuery(
        string projectOrSolutionPath,
        bool dryRun = true,
        IEnumerable<string>? excludePackages = null,
        IEnumerable<string>? excludeNamespaces = null,
        IEnumerable<string>? excludeFiles = null)
    {
        if (string.IsNullOrWhiteSpace(projectOrSolutionPath))
            throw new ArgumentException("Project or solution path cannot be null or empty", nameof(projectOrSolutionPath));

        ProjectOrSolutionPath = projectOrSolutionPath;
        DryRun = dryRun;
        ExcludePackages = excludePackages?.ToList().AsReadOnly() ?? new List<string>().AsReadOnly();
        ExcludeNamespaces = excludeNamespaces?.ToList().AsReadOnly() ?? new List<string>().AsReadOnly();
        ExcludeFiles = excludeFiles?.ToList().AsReadOnly() ?? new List<string>().AsReadOnly();
    }
}
