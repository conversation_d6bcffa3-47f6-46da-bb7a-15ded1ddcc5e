using Microsoft.Extensions.DependencyInjection;
using NuGone.Application.Shared.Interfaces;
using NuGone.Domain.Features.PackageAnalysis;
using NuGone.Domain.Features.ProjectManagement;
using NuGone.Infrastructure.Features.PackageAnalysis;
using NuGone.Infrastructure.Features.PackageRemoval;
using NuGone.Infrastructure.Features.ProjectManagement;

namespace NuGone.Infrastructure.Shared;

/// <summary>
/// Dependency injection configuration for infrastructure services
/// </summary>
public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services)
    {
        // Register domain service implementations
        services.AddTransient<IProjectReader, ProjectReader>();
        services.AddTransient<IPackageUsageAnalyzer, PackageUsageAnalyzer>();
        
        // Register application service implementations
        services.AddTransient<IPackageRemover, PackageRemover>();
        services.AddTransient<IProjectBuilder, ProjectBuilder>();

        return services;
    }
}
